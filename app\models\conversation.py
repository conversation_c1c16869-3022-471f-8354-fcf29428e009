from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId
from app.config.settings import settings

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, _):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema):
        field_schema.update(type="string")

class Message(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    conversation_id: PyObjectId
    message_id: Optional[str] = None
    user_id: int
    content: str
    role: str  # "user" or "ai"
    feedback_message: Optional[str] = Field(default=None, description = "Feedback message provided by user")
    feedback_created_at: Optional[datetime] = Field(default=None, description ="When was the feedback provided.")
    feedback_flag: Optional[int] = Field(default=None, description = " Flag indication if feedback was provide by user, if 1 it means liked, 0 means otherwise, null means didn't provded any feedback")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="When the message was last updated")
    inventory_vehicles: Optional[List[Dict[str, Any]]] = Field(default=None, description="Vehicle inventory data when available")
    is_inventory_fetch: Optional[int] = Field(default=None, description="Flag indicating if inventory was fetched (1 if yes, None if no)")
    total_vehicle_count: int = Field(default=0, description="Total count of vehicles matching the search criteria")
    fitlers: Dict[str,Any] = Field(default={}, description="Filters used to fetch the inventory")
    class Config:
        allow_population_by_field_name = True
        json_encoders = {ObjectId: str}

class Conversation(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: int
    title: str
    deleted_at: Optional[datetime] = Field(default=None, description = "Deleted at is the time when the message was deleted at. By default it is null.")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    messages: List[Message] = []

    class Config:
        allow_population_by_field_name = True
        json_encoders = {ObjectId: str}
